package websocket

import (
	"encoding/json"
	"net/http"
	"sync"

	"fin_gateway/internal/grpcclient"
	"fin_gateway/internal/rabbitmq"
	intent "fin_gateway/proto/intent"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

type Server struct {
	upgrader   websocket.Upgrader
	clients    map[string]*websocket.Conn
	mu         sync.Mutex
	logger     *zap.Logger
	publisher  *rabbitmq.Publisher
	intentGrpc *grpcclient.IntentClient
}

func NewServer(logger *zap.Logger, pub *rabbitmq.Publisher, ic *grpcclient.IntentClient) *Server {
	return &Server{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true },
		},
		clients:    make(map[string]*websocket.Conn),
		logger:     logger,
		publisher:  pub,
		intentGrpc: ic,
	}
}

// Sesuaikan dengan payload fin_chat
type IncomingMsg struct {
	ID        string `json:"id"`
	Type      string `json:"type"`
	Timestamp int64  `json:"timestamp"`
	Payload   struct {
		Messages []struct {
			Role    string `json:"role"`
			Content string `json:"content"`
			Parts   []struct {
				Type string `json:"type"`
				Text string `json:"text"`
			} `json:"parts"`
		} `json:"messages"`
	} `json:"payload"`
}

// HandleWS menangani komunikasi websocket
func (s *Server) HandleWS(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		s.logger.Error("ws upgrade failed", zap.Error(err))
		return
	}
	defer conn.Close()

	for {
		_, msg, err := conn.ReadMessage()
		if err != nil {
			s.logger.Error("read ws msg failed", zap.Error(err))
			break
		}

		var in IncomingMsg
		if err := json.Unmarshal(msg, &in); err != nil {
			s.logger.Error("invalid ws msg", zap.ByteString("raw", msg), zap.Error(err))
			continue
		}

		// Validasi messages
		if len(in.Payload.Messages) == 0 {
			s.logger.Warn("no messages found in payload", zap.Any("incoming", in))
			continue
		}

		firstMsg := in.Payload.Messages[0]

		// Buat gRPC request sesuai proto IntentRequest
		grpcReq := &intent.IntentRequest{
			Id: in.ID,
			Messages: []*intent.Message{
				{
					Role:    firstMsg.Role,
					Content: firstMsg.Content,
					Parts: []*intent.MessagePart{
						{Type: "text", Text: firstMsg.Content},
					},
				},
			},
			// UserId, SessionId, UserEmail bisa ditambahkan kalau nanti ada di payload
		}

		s.logger.Info("sending DetectIntent gRPC request",
			zap.String("id", in.ID),
			zap.String("role", firstMsg.Role),
			zap.String("content", firstMsg.Content),
		)

		intentResp, err := s.intentGrpc.DetectIntent(grpcReq)
		if err != nil {
			s.logger.Error("grpc DetectIntent failed", zap.Error(err))
			continue
		}

		out, _ := json.Marshal(map[string]interface{}{
			"id":         in.ID,
			"type":       in.Type,
			"text":       firstMsg.Content,
			"intent":     intentResp.Intent.Label,
			"confidence": intentResp.Intent.Confidence,
			"meta":       intentResp.Intent.Meta,
		})

		// Publish ke RabbitMQ
		if err := s.publisher.Publish("chat.input", out); err != nil {
			s.logger.Error("publish failed", zap.Error(err))
		} else {
			s.logger.Info("published to chat.input", zap.String("id", in.ID))
		}
	}
}
