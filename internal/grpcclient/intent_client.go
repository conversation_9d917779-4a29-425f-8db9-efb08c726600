package grpcclient

import (
	"context"
	"time"

	intent "fin_gateway/proto/intent"

	"google.golang.org/grpc"
)

type IntentClient struct {
	conn   *grpc.ClientConn
	client intent.IntentServiceClient
}

func New(addr string) (*IntentClient, error) {
	// gunakan WithInsecure hanya untuk dev; prod pakai TLS
	conn, err := grpc.Dial(addr, grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	return &IntentClient{
		conn:   conn,
		client: intent.NewIntentServiceClient(conn),
	}, nil
}

func (c *IntentClient) DetectIntent(req *intent.IntentRequest) (*intent.IntentResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return c.client.DetectIntent(ctx, req)
}

func (c *IntentClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
